// Auto-generated with extract_plugin_manifest_strings.py, do not edit!

from django.utils.translation import gettext as _
_("Upload and tile ODM assets with Cesium ion.")
_("Import images from external sources directly")
_("Compute, preview and export contours from DEMs")
_("Display program version, memory and disk space usage statistics")
_("Integrate WebODM with DroneDB: import images and share results")
_("Create editable short links when sharing task URLs")
_("Add a fullscreen button to the 2D map view")
_("Process in the cloud with webodm.net")
_("Compute volume, area and length measurements on Leaflet")
_("A plugin to upload orthophotos to OpenAerialMap")
_("A plugin to add a button for quickly opening OpenStreetMap's iD editor and setup a TMS basemap.")
_("A plugin to create GCP files from images")
_("A plugin to show charts of projects and tasks")
_("Create short links when sharing task URLs")
_("Get notified when a task has finished processing, has been removed or has failed")
_("A plugin to create GCP files from images")
_("Annotate and measure on 2D maps with ease")
_("Add a GPS location button to the 2D map view")
_("Plugin to get align from external service for WebODM")
_("Detect objects using AI in orthophotos")
