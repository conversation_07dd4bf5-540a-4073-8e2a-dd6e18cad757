# -*- coding: utf-8 -*-
# Generated by Django 1.11.1 on 2017-11-30 15:41
from __future__ import unicode_literals

from django.db import migrations, models
import uuid, os, pickle, tempfile

from webodm import settings

tasks = []
task_ids = {} # map old task IDs --> new task IDs

def dump(apps, schema_editor):
    global tasks, task_ids

    Task = apps.get_model('app', 'Task')

    tasks = list(Task.objects.all().values('id', 'project'))

    # Generate UUIDs
    for task in tasks:
        new_id = uuid.uuid4()

        # Save reference to it
        task['new_id'] = new_id

        # Populate map
        task_ids[task['id']] = new_id

    tmp_path = os.path.join(tempfile.gettempdir(), "public_task_uuids_migration.pickle")
    pickle.dump((tasks, task_ids), open(tmp_path, 'wb'))

    if len(tasks) > 0: print("Dumped tasks")


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0011_auto_20171109_1237'),
    ]

    operations = [
        migrations.AddField(
            model_name='task',
            name='public',
            field=models.BooleanField(default=False, help_text='A flag indicating whether this task is available to the public'),
        ),

        migrations.RunPython(dump),

        migrations.RemoveField(
            model_name='imageupload',
            name='task'
        ),
        migrations.AddField(
            model_name='task',
            name='new_id',
            field=models.UUIDField(null=True)
        ),
    ]
