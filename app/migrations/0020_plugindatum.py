# Generated by Django 2.0.3 on 2018-07-24 21:01

from django.conf import settings
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('app', '0019_remove_task_processing_lock'),
    ]

    operations = [
        migrations.CreateModel(
            name='PluginDatum',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(db_index=True, help_text='Setting key', max_length=255)),
                ('int_value', models.IntegerField(blank=True, default=None, help_text='Integer value', null=True)),
                ('float_value', models.FloatField(blank=True, default=None, help_text='Float value', null=True)),
                ('bool_value', models.NullB<PERSON>ean<PERSON>ield(default=None, help_text='Bool value')),
                ('string_value', models.TextField(blank=True, default=None, help_text='String value', null=True)),
                ('json_value', django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=None, help_text='JSON value', null=True)),
                ('user', models.ForeignKey(help_text='The user this setting belongs to. If NULL, the setting is global.', on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
