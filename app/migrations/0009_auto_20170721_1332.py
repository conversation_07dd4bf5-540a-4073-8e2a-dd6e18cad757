# -*- coding: utf-8 -*-
# Generated by Django 1.11.1 on 2017-07-21 17:32
from __future__ import unicode_literals

import app.models.task
import django.contrib.postgres.fields.jsonb
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0008_preset'),
    ]

    operations = [
        migrations.AlterField(
            model_name='preset',
            name='options',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=[], help_text="Options that define this preset (same format as in a Task's options).", validators=[app.models.task.validate_task_options]),
        ),
    ]
