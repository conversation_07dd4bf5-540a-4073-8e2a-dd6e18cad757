# Generated by Django 2.2.27 on 2025-03-21 16:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0041_auto_20250311_2007'),
    ]

    operations = [
        migrations.AddField(
            model_name='project',
            name='public',
            field=models.BooleanField(default=False, help_text='A flag indicating whether this project is available to the public', verbose_name='Public'),
        ),
        migrations.AddField(
            model_name='project',
            name='public_edit',
            field=models.BooleanField(default=False, help_text='A flag indicating whether this public project can be edited', verbose_name='Public Edit'),
        ),
        migrations.AddField(
            model_name='project',
            name='public_id',
            field=models.UUIDField(blank=True, db_index=True, default=None, help_text='Public identifier of the project', null=True, unique=True, verbose_name='Public Id'),
        ),
    ]
