# -*- coding: utf-8 -*-
# Generated by Django 1.11.1 on 2017-11-09 17:37
from __future__ import unicode_literals

import colorfield.fields
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0010_auto_20170725_1324'),
    ]

    operations = [
        migrations.CreateModel(
            name='Setting',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('app_name', models.CharField(help_text='The name of your application', max_length=255)),
                ('app_logo', models.ImageField(help_text='A 512x512 logo of your application (.png or .jpeg)', upload_to='settings/')),
                ('organization_name', models.CharField(blank=True, default='WebODM', help_text='The name of your organization', max_length=255, null=True)),
                ('organization_website', models.URL<PERSON>ield(blank=True, default='https://github.com/OpenDroneMap/WebODM/', help_text='The website URL of your organization', max_length=255, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Theme',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name of theme', max_length=255)),
                ('primary', colorfield.fields.ColorField(default='#2c3e50', help_text='Most text, icons, and borders.', max_length=18)),
                ('secondary', colorfield.fields.ColorField(default='#ffffff', help_text='The main background color, and text color of some buttons.', max_length=18)),
                ('tertiary', colorfield.fields.ColorField(default='#18bc9c', help_text='Navigation links.', max_length=18)),
                ('button_primary', colorfield.fields.ColorField(default='#2c3e50', help_text='Primary button color.', max_length=18)),
                ('button_default', colorfield.fields.ColorField(default='#95a5a6', help_text='Default button color.', max_length=18)),
                ('button_danger', colorfield.fields.ColorField(default='#e74c3c', help_text='Delete button color.', max_length=18)),
                ('header_background', colorfield.fields.ColorField(default='#18bc9c', help_text="Background color of the site's header.", max_length=18)),
                ('header_primary', colorfield.fields.ColorField(default='#ffffff', help_text="Text and icons in the site's header.", max_length=18)),
                ('border', colorfield.fields.ColorField(default='#e7e7e7', help_text='The color of most borders.', max_length=18)),
                ('highlight', colorfield.fields.ColorField(default='#f7f7f7', help_text='The background color of panels and some borders.', max_length=18)),
                ('dialog_warning', colorfield.fields.ColorField(default='#f39c12', help_text='The border color of warning dialogs.', max_length=18)),
                ('failed', colorfield.fields.ColorField(default='#ffcbcb', help_text='The background color of failed notifications.', max_length=18)),
                ('success', colorfield.fields.ColorField(default='#cbffcd', help_text='The background color of success notifications.', max_length=18)),
                ('css', models.TextField(blank=True, default='')),
                ('html_before_header', models.TextField(blank=True, default='')),
                ('html_after_header', models.TextField(blank=True, default='')),
                ('html_after_body', models.TextField(blank=True, default='')),
                ('html_footer', models.TextField(blank=True, default='')),
            ],
        ),
        migrations.AddField(
            model_name='setting',
            name='theme',
            field=models.ForeignKey(help_text='Active theme', on_delete=django.db.models.deletion.DO_NOTHING, to='app.Theme'),
        ),
    ]
