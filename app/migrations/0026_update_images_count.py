# -*- coding: utf-8 -*-
# Generated by Django 1.11.1 on 2017-07-07 18:05
from __future__ import unicode_literals

from django.db import migrations


def update_images_count(apps, schema_editor):
    Task = apps.get_model('app', 'Task')

    for t in Task.objects.all():
        print("Updating {}".format(t))
        t.images_count = len(t.scan_images())
        t.save()


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0025_auto_20190220_1854'),
    ]

    operations = [
        migrations.RunPython(update_images_count),
    ]
