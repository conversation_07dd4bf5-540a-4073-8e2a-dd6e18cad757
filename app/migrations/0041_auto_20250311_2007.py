# Generated by Django 2.2.27 on 2025-03-11 20:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0040_auto_20241106_1832'),
    ]

    operations = [
        migrations.AddField(
            model_name='task',
            name='compacted',
            field=models.BooleanField(default=False, help_text='A flag indicating whether this task was compacted', verbose_name='Compact'),
        ),
        migrations.AlterField(
            model_name='task',
            name='pending_action',
            field=models.IntegerField(blank=True, choices=[(1, 'CANCEL'), (2, 'REMOVE'), (3, 'RESTART'), (4, 'RESIZE'), (5, 'IMPORT'), (6, 'COMPACT')], db_index=True, help_text='A requested action to be performed on the task. The selected action will be performed by the worker at the next iteration.', null=True, verbose_name='Pending Action'),
        ),
    ]
