# Generated by Django 2.1.5 on 2019-02-20 18:54

import app.models.task
import colorfield.fields
import django.contrib.postgres.fields
import django.contrib.postgres.fields.jsonb
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0024_update_task_assets'),
    ]

    operations = [
        migrations.AddField(
            model_name='task',
            name='import_url',
            field=models.TextField(blank=True, default='',
                                   help_text='URL this task is imported from (only for imported tasks)'),
        ),
        migrations.AlterField(
            model_name='preset',
            name='options',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=list,
                                                                 help_text="Options that define this preset (same format as in a Task's options).",
                                                                 validators=[app.models.task.validate_task_options]),
        ),
        migrations.AlterField(
            model_name='task',
            name='available_assets',
            field=django.contrib.postgres.fields.ArrayField(base_field=models.CharField(max_length=80), blank=True,
                                                            default=list,
                                                            help_text='List of available assets to download',
                                                            size=None),
        ),
        migrations.AlterField(
            model_name='task',
            name='options',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=dict,
                                                                 help_text='Options that are being used to process this task',
                                                                 validators=[app.models.task.validate_task_options]),
        ),
        migrations.AlterField(
            model_name='task',
            name='pending_action',
            field=models.IntegerField(blank=True, choices=[(1, 'CANCEL'), (2, 'REMOVE'), (3, 'RESTART'), (4, 'RESIZE'),
                                                           (5, 'IMPORT')], db_index=True,
                                      help_text='A requested action to be performed on the task. The selected action will be performed by the worker at the next iteration.',
                                      null=True),
        ),
        migrations.AlterField(
            model_name='theme',
            name='header_background',
            field=colorfield.fields.ColorField(default='#3498db', help_text="Background color of the site's header.",
                                               max_length=18),
        ),
        migrations.AlterField(
            model_name='theme',
            name='tertiary',
            field=colorfield.fields.ColorField(default='#3498db', help_text='Navigation links.', max_length=18),
        ),
        migrations.AddField(
            model_name='task',
            name='images_count',
            field=models.IntegerField(blank=True, default=0, help_text='Number of images associated with this task'),
        ),
    ]
