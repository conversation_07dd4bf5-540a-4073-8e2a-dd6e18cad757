# -*- coding: utf-8 -*-
# Generated by Django 1.11.1 on 2017-11-30 15:41
from __future__ import unicode_literals

from django.db import migrations, models
import uuid

from webodm import settings

class Migration(migrations.Migration):

    dependencies = [
        ('app', '0013_public_task_uuids'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='task',
            name='new_id',
            field=models.UUIDField(default=uuid.uuid4, unique=True, serialize=False, editable=False)
        ),
        migrations.RemoveField('task', 'id'),
        migrations.RenameField(
            model_name='task',
            old_name='new_id',
            new_name='id'
        ),
        migrations.AlterField(
            model_name='task',
            name='id',
            field=models.UUIDField(primary_key=True, default=uuid.uuid4, unique=True, serialize=False, editable=False)
        ),

        migrations.AddField(
            model_name='imageupload',
            name='task',
            field=models.Foreign<PERSON>ey(null=True, on_delete=models.CASCADE, help_text="Task this image belongs to", to='app.Task')
        ),

    ]
