# Generated by Django 2.2.27 on 2025-05-02 19:45
from django.db import migrations
import os
from webodm import settings


def update_task_console_link(apps, schema_editor):
    Task = apps.get_model('app', 'Task')

    for t in Task.objects.all():
        task_output = os.path.join(settings.MEDIA_ROOT, "project", str(t.project.id), "task", str(t.id), "assets", "task_output.txt")
        console_output = os.path.join(settings.MEDIA_ROOT, "project", str(t.project.id), "task", str(t.id), "data", "console_output.txt")

        if os.path.isfile(task_output) and os.path.isfile(console_output):
            print("Update {}".format(t))
            # Guarantee consistency, save space
            try:
                if os.path.isfile(console_output):
                    os.unlink(console_output)
                
                os.link(task_output, console_output)
            except OSError:
                print("Cannot link console file: %s --> %s" % (task_output, console_output))


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0043_task_crop'),
    ]

    operations = [
        migrations.RunPython(update_task_console_link),
    ]

