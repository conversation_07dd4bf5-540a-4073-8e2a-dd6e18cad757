# Generated by Django 2.0.3 on 2018-12-05 16:44

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0021_auto_20180726_1746'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='task',
            name='ground_control_points',
        ),
        migrations.AddField(
            model_name='task',
            name='resize_progress',
            field=models.FloatField(blank=True, default=0.0, help_text="Value between 0 and 1 indicating the resize progress of this task's images"),
        ),
        migrations.AddField(
            model_name='task',
            name='upload_progress',
            field=models.FloatField(blank=True, default=0.0, help_text="Value between 0 and 1 indicating the upload progress of this task's files to the processing node"),
        ),
    ]
