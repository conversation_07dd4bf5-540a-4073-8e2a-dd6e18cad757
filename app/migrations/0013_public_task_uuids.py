# -*- coding: utf-8 -*-
# Generated by Django 1.11.1 on 2017-11-30 15:41
from __future__ import unicode_literals

from django.db import migrations, models
import uuid, os, pickle, tempfile

from webodm import settings

tasks = []
task_ids = {} # map old task IDs --> new task IDs

def task_path(project_id, task_id):
    return os.path.join(settings.MEDIA_ROOT,
                        "project",
                        str(project_id),
                        "task",
                        str(task_id))

def rename_task_folders(apps, schema_editor):
    global tasks, task_ids

    for t in tasks:
        print("Checking task {}".format(t['id']))
        current_path = task_path(t['project'], t['id'])
        if os.path.exists(current_path):
            new_path = task_path(t['project'], task_ids[t['id']])
            print("Migrating {} --> {}".format(current_path, new_path))
            os.rename(current_path, new_path)

def create_uuids(apps, schema_editor):
    global tasks, task_ids

    Task = apps.get_model('app', 'Task')
    for task in tasks:
        print(task)

        t = Task.objects.get(id=task['id'])
        t.new_id = task['new_id']
        t.save()

    if len(tasks) > 0: print("Created UUIDs")


def restore(apps, schema_editor):
    global tasks, task_ids
    
    tmp_path = os.path.join(tempfile.gettempdir(), "public_task_uuids_migration.pickle")
    tasks, task_ids = pickle.load(open(tmp_path, 'rb'))


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0012_public_task_uuids'),
    ]

    operations = [
        migrations.RunPython(restore),
        migrations.RunPython(create_uuids),
        migrations.RunPython(rename_task_folders),       
    ]
