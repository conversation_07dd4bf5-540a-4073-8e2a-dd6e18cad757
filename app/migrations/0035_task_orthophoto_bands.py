# Generated by Django 2.2.27 on 2023-05-19 15:38

import rasterio
import os
import django.contrib.postgres.fields.jsonb
from django.db import migrations
from webodm import settings

def update_orthophoto_bands_fields(apps, schema_editor):
    Task = apps.get_model('app', 'Task')

    for t in Task.objects.all():

        bands = []
        orthophoto_path = os.path.join(settings.MEDIA_ROOT, "project", str(t.project.id), "task", str(t.id), "assets", "odm_orthophoto", "odm_orthophoto.tif")

        if os.path.isfile(orthophoto_path):
            try:
                with rasterio.open(orthophoto_path) as f:
                    bands = [c.name for c in f.colorinterp]
            except Exception as e:
                print(e)

        print("Updating {} (with orthophoto bands: {})".format(t, str(bands)))

        t.orthophoto_bands = bands
        t.save()


class Migration(migrations.Migration):

    dependencies = [
        ('app', '0034_delete_imageupload'),
    ]

    operations = [
        migrations.AddField(
            model_name='task',
            name='orthophoto_bands',
            field=django.contrib.postgres.fields.jsonb.JSONField(blank=True, default=list, help_text='List of orthophoto bands', verbose_name='Orthophoto Bands'),
        ),

        migrations.RunPython(update_orthophoto_bands_fields),
    ]
