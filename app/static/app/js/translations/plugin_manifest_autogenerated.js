// Auto-generated with extract_plugin_manifest_strings.py, do not edit!

_("Compute volume, area and length measurements on Leaflet");
_("A plugin to create GCP files from images");
_("A plugin to create GCP files from images");
_("A plugin to add a button for quickly opening OpenStreetMap's iD editor and setup a TMS basemap.");
_("A plugin to upload orthophotos to OpenAerialMap");
_("Display program version, memory and disk space usage statistics");
_("Sync accounts from webodm.net");
_("Add a fullscreen button to the 2D map view");
_("Compute, preview and export contours from DEMs");
_("Calculate and draw an elevation map based on a task's DEMs");
_("Upload and tile ODM assets with Cesium ion.");
_("Import images from external sources directly");
_("Detect changes between two different tasks in the same project.");
