// Auto-generated with extract_potree_strings.py, do not edit!

_("Navigation");
_("Appearance");
_("Tools");
_("Measurements");
_("Clipping");
_("Annotations");
_("Materials");
_("Scene");
_("Classification filter");
_("Filters");
_("Other settings");
_("About");
_("Angle measurement");
_("Point measurement");
_("Distance measurement");
_("Height measurement");
_("Circle measurement");
_("Area measurement");
_("Volume measurement");
_("Height profile");
_("Annotation");
_("Volume clip");
_("Polygon clip");
_("Draw a selection box. Requires you to be in orthographic camera mode!");
_("Clip plane on x axis");
_("Clip plane on y axis");
_("Clip plane on z axis");
_("Remove all measurements");
_("Left view");
_("Righ view");
_("Front view");
_("Back view");
_("Top view");
_("Bottom view");
_("Full extent");
_("Orbit control");
_("Fly control");
_("Helicopter control");
_("Earth control");
_("Perspective camera");
_("Orthographic camera");
_("Navigation cube");
_("Remove all clipping volumes");
_("Compass");
_("Camera Animation");
_("Remove last camera animation");
_("Point budget");
_("Point size");
_("Minimum size");
_("Opacity");
_("Field of view");
_("Point sizing");
_("Materials");
_("Elevation range");
_("Scalar range");
_("Quality");
_("Shape");
_("Radius");
_("Strength");
_("Opacity");
_("Enable");
_("Min node size");
_("Clip mode");
_("Speed");
_("Sky");
_("Keep above ground");
_("Box");
_("Length unit");
_("Lock view");
_("Language");
_("Backface Culling");
_("clip");
_("show volume");
_("show in 3D");
_("show on map");
_("Number of Points");
_("Height profile");
_("Save LAS(3D)");
_("Save CSV(2D)");
_("Camera Position");
_("Camera Target");
_("Return Number");
_("Number of Returns");
_("min");
_("max");
_("GPS Time");
_("Language");
