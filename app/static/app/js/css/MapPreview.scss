.map-preview{
    position: relative;
    margin-bottom: 16px;
    border-radius: 3px;
    .leaflet-container, .standby .cover{
        border-radius: 3px;
    }

    .standby{
        z-index: 1001;
    }

    .download-control{
        position: absolute;
        left: 8px;
        top: 8px;
        z-index: 1000;
        .btn:active, .btn:focus{
            outline: none;
        }
    }

    .leaflet-control-layers-expanded{
        .leaflet-control-layers-base{
            overflow: hidden;
        }
        height: 200px;
        overflow: hidden;
    }

    .crop-button-delete{
        min-width: 70px;
    }

    .plot-warning{
        position: absolute;
        z-index: 999;
        left: 58px;
        top: 8px;
        padding: 8px;
        border-radius: 4px;
    }
}