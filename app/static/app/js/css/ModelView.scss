.model-view{
    position: relative;
    height: 100%;

    #potree_render_area > canvas{
        width: 100% !important;
        height: 100% !important;

        &.pointer-cursor{
            cursor: pointer;
        }
    }

    .container{
        background: rgb(79,79,79);
        background: -moz-radial-gradient(center, ellipse cover, rgba(79,79,79,1) 0%, rgba(22,22,22,1) 100%);
        background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,rgba(79,79,79,1)), color-stop(100%,rgba(22,22,22,1)));
        background: -webkit-radial-gradient(center, ellipse cover, rgba(79,79,79,1) 0%,rgba(22,22,22,1) 100%);
        background: -o-radial-gradient(center, ellipse cover, rgba(79,79,79,1) 0%,rgba(22,22,22,1) 100%);
        background: -ms-radial-gradient(center, ellipse cover, rgba(79,79,79,1) 0%,rgba(22,22,22,1) 100%);
        background: radial-gradient(ellipse at center, rgba(79,79,79,1) 0%,rgba(22,22,22,1) 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#4f4f4f', endColorstr='#161616',GradientType=1 );
    	position: relative;
    	padding: 0;
    }
    .dg{
	    &.main{
		    position: absolute;
		    right: 0px;
	    }
	    .c{
	    	select{
	    		color: black;
	    	}
	    }
    }

    input[type="checkbox"]{
        position: relative;
        top: 2px;
    }

    .action-buttons{
        padding: 12px;
        background-color: #19282c;

        .textured-model-chkbox-container{
            margin-bottom: 8px;
        }

        label{
            color: #7a8184;
        }

        .asset-download-buttons{
            margin-right: 8px;
            &.open{
                button{
                    outline: none;
                }
            }
        }

    }
    .model-action-buttons{
        position: absolute;
        z-index: 1;
        bottom: 12px;
        right: 6px;

        &.modal-open{
            z-index: 999999;
        }

        .switchModeButton{
            position: initial;
        }
        .asset-download-buttons{
            .dropdown-menu{
                left: -150px;
            }
        }

        & > *{
            display: inline-block;
            margin-right: 8px;
        }
    }

    /* Potree specific */
    #potree_map{
        position: absolute; 
        left: 50px; 
        top: 50px; 
        width: 400px; 
        height: 400px;
        display: none
    }

    #potree_menu{
        input{
            color: buttontext;
        }
        legend{
            display: initial !important;
            width: initial !important;
            padding: initial !important;
            margin-bottom: initial !important;
            font-size: inherit !important;
            line-height: initial !important;
            color: inherit !important;
            border: initial !important;
            border-bottom: initial !important;
        }

        #show_2d_profile{
            color: initial !important;
        }
        
        h3{
            font-size: 14px;
        }
    }

    #potree_map_header{
        position: absolute; 
        width: 100%; 
        height: 25px; 
        top: 0px; 
        background-color: rgba(0,0,0,0.5); 
        z-index: 1000;
        border-top-left-radius: 3px; 
        border-top-right-radius: 3px;
    }

    #potree_map_content{
        position: absolute; 
        z-index: 100; 
        top: 25px; 
        width: 100%; 
        height: calc(100% - 25px); 
        border: 2px solid rgba(0,0,0,0.5); 
        box-sizing: border-box;
    }

    #potree_sidebar_container{
        overflow-y: auto;
        background-color: #19282c;

        a{
            color: #111;
        }  

        #sidebar_root{
            width: 300px;

            .pv-menu-list{
                padding-right: 12px;

                .divider{
                    padding: 10px 0px 15px 0px;
                }

                a{
                    color: #8Aa1c4;
                } 
            }

            .measurement-panel-remove:hover{
                cursor: pointer;
            }

            position: absolute; 
            min-height: 100%; 
            height: 100%;

            .potree_sidebar_brand{
                display: flex; 
                flex-direction: row;
            }

            #potree_version_number{
                color: #9AA1A4; 
                font-size: 80%; 
                font-weight: 100;
            }
        }
    }

    
    #potree_download_profile_ortho_link, #potree_download_profile_link{
        color: black;
    }

    .thumbnail{
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 10;
        background: #19282c;
        border-color: #a6a9aa;
        color: #ccccff;
        .close-thumb{
            opacity: 0.8;
            position: absolute;
            top: 4px;
            right: 8px;
            &:hover{
                opacity: 1;
            }
        }
    }
}

#profile_window{
    z-index: 999999999999 !important;
}