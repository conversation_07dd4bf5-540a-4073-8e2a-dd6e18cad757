.map-view{
    .map-title{
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }
    height: calc(100% - 20px);
    position: relative;

    input.opacity[type="range"]{
        margin-left: 4px;
        display: inline-block;
        width: 130px;
        position: relative;
        top: 6px;
    }

    .map-container{
        height: 100%;
        position: relative;
        .standby{
            z-index: 99999;
        }
    }

    .map-view-header{
        display: flex;
        justify-content: space-between;
        .map-type-selector{
            flex: none;
        }
    }

    .crop-button i{
        color: #191919;
    }
}