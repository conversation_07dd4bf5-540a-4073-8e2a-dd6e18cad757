.leaflet-control-layers-control .layers-control-panel{
  padding: 6px 10px 6px 6px;
  background: #fff;
  width: 300px;
  min-height: 74px;
  max-height: 418px;
  overflow-y: auto;

  .loading{
      margin-top: 6px;
      margin-left: 4px;
  }

  .close-button{
    display: inline-block;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAQAAAD8x0bcAAAAkUlEQVR4AZWRxQGDUBAFJ9pMflNIP/iVSkIb2wgccXd7g7O+3JXCQUgqBAfFSl8CMooJGQHfuUlEwZpoahZQ7ODTSXWJQkxyioock7BL2tXmdF4moJNX6IDZfbUBQNrX7qfeXfPuqwBAQjEz60w64htGJ+luFH48gt+NYe6v5b/cnr9asM+HlRQ2Qlwh2CjuqQQ9vKsKTwhQ1wAAAABJRU5ErkJggg==);
    height: 18px;
    width: 18px;
    margin-right: 0;
    float: right;
    vertical-align: middle;
    text-align: right;
    margin-top: 0px;
    margin-left: 16px;
    position: relative;
    left: 2px;

    &:hover{
      opacity: 0.7;
      cursor: pointer;
    }
  }

  .title{
    font-size: 120%;
    margin-right: 60px;
  }

  hr{
    clear: both;
    margin: 6px 0px;
    border-color: #ddd;
  }

  *{
      font-size: 12px;
  }

  .layer-group-title{
    font-weight: bold;
    margin-top: 2px;
    margin-bottom: 2px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
