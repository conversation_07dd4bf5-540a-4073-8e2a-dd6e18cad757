{"version": 3, "sources": ["forms.scss"], "names": [], "mappings": "AAAA,0BAAY,CAGZ,sBAEQ,eAAgB,CAChB,YAAa,CACb,cAAe,CACf,4BAA6B,CALrC,sDASQ,qBAAsB,CAT9B,iDAaQ,YAAa,CACb,gBAAiB,CAdzB,6BAkBQ,cAAe,CAlBvB,oBAsBQ,YAAa,CAtBrB,kBA4BQ,eAAmB,CACnB,UAAW,CACX,cAAe,CA9BvB,uDAkCQ,eAAiB,CACjB,UAAW,CAnCnB,iCAyCQ,oBAAqB,CAzC7B,oCA6CQ,UAAW,CACX,cAAe,CA9CvB,gDAkDQ,mBAAoB,CACpB,SAAU,CAnDlB,2BAuDQ,aAAc,CACd,SAAU,CAxDlB,8BA4DQ,UAAW,CACX,iBAAkB,CA7D1B,2BAmEQ,aAAc,CACd,oBAAqB,CACrB,UAAW,CACX,WAAY,CACZ,oBAAqB,CACrB,aAAc,CAxEtB,sDA4EQ,UAAW,CACX,oBAAqB,CACrB,qBAAsB,CACtB,WAAY,CA/EpB,6BAmFQ,aAAc,CACd,YAAa,CACb,eAAgB,CAChB,iBAAkB,CAtF1B,8BA0FQ,cAAe,CACf,UAAW,CACX,UAAW,CA5FnB,qCAgGQ,eAAgB,CAhGxB,6FAoGQ,WAAY,CApGpB,6BAwGQ,eAAgB,CAChB,gBAAiB,CAzGzB,uCA6GQ,oBAAqB,CACrB,QAAS,CACT,SAAU,CA/GlB,iCAmHQ,UAAW,CACX,YAAa,CACb,iBAAkB,CAClB,cAAiB,CAEjB,yBAxHR,iCAyHY,uBAAyB,CACzB,wBAA0B,CAC1B,iBAAkB,CAClB,kBAAmB,CAG1B,CA/HL,uCAkIQ,aAAc,CACd,cAAe,CAnIvB,4CAuIQ,eAAgB,CAChB,gBAAiB,CAxIzB,yHA8IQ,iBAAkB,CAClB,cAAiB,CA/IzB,gCAmJQ,eAAgB,CAnJxB,kCAuJQ,aAAc,CACd,cAAe,CAxJvB,qCA4JQ,UAAW,CACX,UAAW,CACX,oBAAqB,CACrB,mBAAoB,CACpB,mBAAoB,CAhK5B,4CAoKQ,eAAgB,CApKxB,2FAwKQ,WAAY,CAxKpB,iCA4KQ,aAAc,CACd,cAAe,CA7KvB,gCAiLQ,UAAW,CACX,iBAAkB,CAlL1B,wBAwLQ,WAAY,CAxLpB,6DA4LQ,iBAAkB,CA5L1B,8BAgMQ,iBAAkB,CAhM1B,qGAoMQ,WAAY,CApMpB,iCA0MQ,YAAa,CA1MrB,iEA8MQ,aAAc,CA9MtB,+BAkNQ,qBAAsB,CACtB,iBAAkB,CAClB,eAAgB,CApNxB,kCAwNQ,kBAAmB,CACnB,UAAW,CAzNnB,sCA6NQ,UAAW,CA7NnB,gDAiOQ,sBAAuB,CACvB,cAAe,CACf,aAAc,CAnOtB,wCAyOQ,yEAAkF,CAzO1F,wBA+OQ,iBAAkB,CAClB,kBAAmB,CACnB,mBAAoB,CAEpB,eAAgB,CAChB,kBAAmB,CACnB,qBAAsB,CACtB,iBAAkB,CAClB,gBAAiB,CACjB,eAAgB,CAxPxB,mCA4PQ,aAAc,CA5PtB,8BAgQQ,WAAY,CACZ,gBAAiB,CACjB,mBAAoB,CAlQ5B,sCAsQQ,mBAAoB,CACpB,wBAAyB,CAvQjC,0BA2QQ,WAAa,CA3QrB,yCA+QQ,UAAW,CACX,eAAgB,CAhRxB,qCAoRQ,aAAc,CACd,kBAAmB,CACnB,iBAAkB,CAClB,iBAAkB,CAClB,gBAAiB,CACjB,UAAW,CAzRnB,kIA+RQ,kBAAmB,CA/R3B,kCAqSQ,kBAAmB,CArS3B,4BAySQ,WAAY,CAzSpB,gDA6SQ,gBAAiB,CACjB,iBAAkB,CA9S1B,wBAkTQ,gBAAiB,CAlTzB,wBAsTQ,eAAgB,CAtTxB,uBA0TQ,UAAW,CA1TnB,6DA8TQ,UAAW,CA9TnB,4CAkUQ,aAAc,CAlUtB,sDAsUQ,WAAY,CAtUpB,wBA0UQ,UAAW,CA1UnB,2BA8UQ,SAAU,CA9UlB,8BAkVQ,UAAW,CAlVnB,wCAsVQ,SAAU,CAtVlB,0BA4VQ,SAAU,CACV,eAAgB,CA7VxB,mCAiWQ,gBAAiB,CAjWzB,yCAqWQ,WAAY,CArWpB,4BAyWQ,iBAAkB,CAzW1B,+BA6WQ,QAAS,CACT,UAAW,CACX,WAAY,CACZ,cAAe,CACf,kBAAmB,CACnB,yBAA0B,CAC1B,4BAA6B,CAnXrC,2CAuXQ,WAAY,CAvXpB,iDA2XQ,eAAgB,CAChB,cAAe,CA5XvB,qCAgYQ,QAAS,CACT,eAAgB,CAChB,WAAY,CACZ,UAAW,CAnYnB,+CAuYQ,QAAS,CACT,mBAAwB,CACxB,cAAe,CACf,eAAgB,CAChB,eAAiB,CACjB,eAAgB,CAChB,UAAW,CA7YnB,mDAiZQ,WAAY,CAjZpB,0DAqZQ,UAAW,CArZnB,mCAyZQ,WAAY,CAzZpB,sDA6ZQ,eAAgB,CA7ZxB,kDAiaQ,eAAkB,CAClB,OAAQ,EACR,iBAAmB,CAna3B,+CAuaQ,OAAU,CACV,SAAU,CAxalB,iDA4aQ,iBAAkB,CAClB,MAAO,CACP,YAAa,CACb,eAAgB,CAChB,eAAgB,CAChB,aAAc,CACd,eAAiB,CACjB,UAAW,EACX,WAAa,CApbrB,mCAwbQ,SAAU,CACV,QAAS,CACT,eAAgB,CA1bxB,sCA8bQ,cAAe,CACf,aAAc,CA/btB,uFAocQ,UAAW,CACX,kBAAmB,CACnB,gBAAiB,CACjB,4BAA6B,CAvcrC,iDA2cQ,gBAAiB,CACjB,4BAA6B,CA5crC,oIAkdQ,uDAAwD,CACxD,iBAAkB,CAClB,cAAe,CApdvB,wBAwdQ,YAAa,CAxdrB,qDA8dQ,eAAgB,CAChB,oBAAqB,CACrB,qBAAsB,CACtB,2BAA4B,CAC5B,oBAAqB,CAle7B,yBAseQ,UAAW,CACX,WAAY,CACZ,6CAA8C,CAxetD,4BA4eQ,UAAW,CACX,WAAY,CACZ,uCAAwC,CA9ehD,4CAkfQ,oBAAqB,CACrB,eAAgB,CAChB,gBAAiB,CApfzB,wCAwfQ,YAAa", "file": "forms.css", "sourcesContent": ["@import url('widgets.css');\n\n/* FORM ROWS */\n.admin-area{\n    .form-row {\n        overflow: hidden;\n        padding: 10px;\n        font-size: 13px;\n        border-bottom: 1px solid #eee;\n    }\n\n    .form-row img, .form-row input {\n        vertical-align: middle;\n    }\n\n    .form-row label input[type=\"checkbox\"] {\n        margin-top: 0;\n        vertical-align: 0;\n    }\n\n    form .form-row p {\n        padding-left: 0;\n    }\n\n    .hidden {\n        display: none;\n    }\n\n    /* FORM LABELS */\n\n    label {\n        font-weight: normal;\n        color: #666;\n        font-size: 13px;\n    }\n\n    .required label, label.required {\n        font-weight: bold;\n        color: #333;\n    }\n\n    /* RADIO BUTTONS */\n\n    form ul.radiolist li {\n        list-style-type: none;\n    }\n\n    form ul.radiolist label {\n        float: none;\n        display: inline;\n    }\n\n    form ul.radiolist input[type=\"radio\"] {\n        margin: -2px 4px 0 0;\n        padding: 0;\n    }\n\n    form ul.inline {\n        margin-left: 0;\n        padding: 0;\n    }\n\n    form ul.inline li {\n        float: left;\n        padding-right: 7px;\n    }\n\n    /* ALIGNED FIELDSETS */\n\n    .aligned label {\n        display: block;\n        padding: 4px 10px 0 0;\n        float: left;\n        width: 160px;\n        word-wrap: break-word;\n        line-height: 1;\n    }\n\n    .aligned label:not(.vCheckboxLabel):after {\n        content: '';\n        display: inline-block;\n        vertical-align: middle;\n        height: 26px;\n    }\n\n    .aligned label + p {\n        padding: 6px 0;\n        margin-top: 0;\n        margin-bottom: 0;\n        margin-left: 170px;\n    }\n\n    .aligned ul label {\n        display: inline;\n        float: none;\n        width: auto;\n    }\n\n    .aligned .form-row input {\n        margin-bottom: 0;\n    }\n\n    .colMS .aligned .vLargeTextField, .colMS .aligned .vXMLLargeTextField {\n        width: 350px;\n    }\n\n    form .aligned ul {\n        margin-left: 6px;\n        padding-left: 3px;\n    }\n\n    form .aligned ul.radiolist {\n        display: inline-block;\n        margin: 0;\n        padding: 0;\n    }\n\n    form .aligned p.help {\n        clear: left;\n        margin-top: 0;\n        margin-left: 160px;\n        padding-left: 0px;\n\n        @media (max-width: 768px) { \n            margin-left: 0 !important;\n            padding-left: 0 !important;\n            padding-left: 16px;\n            padding-right: 16px;\n        }\n\n    }\n\n    form .aligned label + p.help {\n        margin-left: 0;\n        padding-left: 0;\n    }\n\n    form .aligned p.help:last-child {\n        margin-bottom: 0;\n        padding-bottom: 0;\n    }\n\n    form .aligned input + p.help,\n    form .aligned textarea + p.help,\n    form .aligned select + p.help {\n        margin-left: 160px;\n        padding-left: 0px;\n    }\n\n    form .aligned ul li {\n        list-style: none;\n    }\n\n    form .aligned table p {\n        margin-left: 0;\n        padding-left: 0;\n    }\n\n    .aligned .vCheckboxLabel {\n        float: none;\n        width: auto;\n        display: inline-block;\n        vertical-align: -3px;\n        padding: 0 0 5px 5px;\n    }\n\n    .aligned .vCheckboxLabel + p.help {\n        margin-top: -4px;\n    }\n\n    .colM .aligned .vLargeTextField, .colM .aligned .vXMLLargeTextField {\n        width: 610px;\n    }\n\n    .checkbox-row p.help {\n        margin-left: 0;\n        padding-left: 0;\n    }\n\n    fieldset .field-box {\n        float: left;\n        margin-right: 20px;\n    }\n\n    /* WIDE FIELDSETS */\n\n    .wide label {\n        width: 200px;\n    }\n\n    form .wide p, form .wide input + p.help {\n        margin-left: 200px;\n    }\n\n    form .wide p.help {\n        padding-left: 38px;\n    }\n\n    .colM fieldset.wide .vLargeTextField, .colM fieldset.wide .vXMLLargeTextField {\n        width: 450px;\n    }\n\n    /* COLLAPSED FIELDSETS */\n\n    fieldset.collapsed * {\n        display: none;\n    }\n\n    fieldset.collapsed h2, fieldset.collapsed {\n        display: block;\n    }\n\n    fieldset.collapsed {\n        border: 1px solid #eee;\n        border-radius: 4px;\n        overflow: hidden;\n    }\n\n    fieldset.collapsed h2 {\n        background: #f8f8f8;\n        color: #666;\n    }\n\n    fieldset .collapse-toggle {\n        color: #fff;\n    }\n\n    fieldset.collapsed .collapse-toggle {\n        background: transparent;\n        display: inline;\n        color: #447e9b;\n    }\n\n    /* MONOSPACE TEXTAREAS */\n\n    fieldset.monospace textarea {\n        font-family: \"Bitstream Vera Sans Mono\", Monaco, \"Courier New\", Courier, monospace;\n    }\n\n    /* SUBMIT ROW */\n\n    .submit-row {\n        padding-left: 14px;\n        padding-right: 14px;\n        padding-bottom: 10px;\n        //padding: 12px 14px;\n        margin: 0 0 20px;\n        background: #f8f8f8;\n        border: 1px solid #eee;\n        border-radius: 4px;\n        text-align: right;\n        overflow: hidden;\n    }\n\n    body.popup .submit-row {\n        overflow: auto;\n    }\n\n    .submit-row input {\n        height: 35px;\n        line-height: 15px;\n        margin: 10px 0 0 5px;\n    }\n\n    .submit-row input.default {\n        margin: 10px 0 0 8px;\n        text-transform: uppercase;\n    }\n\n    .submit-row p {\n        margin: 0.3em;\n    }\n\n    .submit-row p.deletelink-box {\n        float: left;\n        margin-top: 10px;\n    }\n\n    .submit-row a.deletelink {\n        display: block;\n        background: #ba2121;\n        border-radius: 4px;\n        padding: 10px 15px;\n        line-height: 15px;\n        color: #fff;\n    }\n\n    .submit-row a.deletelink:focus,\n    .submit-row a.deletelink:hover,\n    .submit-row a.deletelink:active {\n        background: #a41515;\n    }\n\n    /* CUSTOM FORM FIELDS */\n\n    .vSelectMultipleField {\n        vertical-align: top;\n    }\n\n    .vCheckboxField {\n        border: none;\n    }\n\n    .vDateField, .vTimeField {\n        margin-right: 2px;\n        margin-bottom: 4px;\n    }\n\n    .vDateField {\n        min-width: 6.85em;\n    }\n\n    .vTimeField {\n        min-width: 4.7em;\n    }\n\n    .vURLField {\n        width: 30em;\n    }\n\n    .vLargeTextField, .vXMLLargeTextField {\n        width: 48em;\n    }\n\n    .flatpages-flatpage #id_content {\n        height: 40.2em;\n    }\n\n    .module table .vPositiveSmallIntegerField {\n        width: 2.2em;\n    }\n\n    .vTextField {\n        width: 20em;\n    }\n\n    .vIntegerField {\n        width: 5em;\n    }\n\n    .vBigIntegerField {\n        width: 10em;\n    }\n\n    .vForeignKeyRawIdAdminField {\n        width: 5em;\n    }\n\n    /* INLINES */\n\n    .inline-group {\n        padding: 0;\n        margin: 0 0 30px;\n    }\n\n    .inline-group thead th {\n        padding: 8px 10px;\n    }\n\n    .inline-group .aligned label {\n        width: 160px;\n    }\n\n    .inline-related {\n        position: relative;\n    }\n\n    .inline-related h3 {\n        margin: 0;\n        color: #666;\n        padding: 5px;\n        font-size: 13px;\n        background: #f8f8f8;\n        border-top: 1px solid #eee;\n        border-bottom: 1px solid #eee;\n    }\n\n    .inline-related h3 span.delete {\n        float: right;\n    }\n\n    .inline-related h3 span.delete label {\n        margin-left: 2px;\n        font-size: 11px;\n    }\n\n    .inline-related fieldset {\n        margin: 0;\n        background: #fff;\n        border: none;\n        width: 100%;\n    }\n\n    .inline-related fieldset.module h3 {\n        margin: 0;\n        padding: 2px 5px 3px 5px;\n        font-size: 11px;\n        text-align: left;\n        font-weight: bold;\n        background: #bcd;\n        color: #fff;\n    }\n\n    .inline-group .tabular fieldset.module {\n        border: none;\n    }\n\n    .inline-related.tabular fieldset.module table {\n        width: 100%;\n    }\n\n    .last-related fieldset {\n        border: none;\n    }\n\n    .inline-group .tabular tr.has_original td {\n        padding-top: 2em;\n    }\n\n    .inline-group .tabular tr td.original {\n        padding: 2px 0 0 0;\n        width: 0;\n        _position: relative;\n    }\n\n    .inline-group .tabular th.original {\n        width: 0px;\n        padding: 0;\n    }\n\n    .inline-group .tabular td.original p {\n        position: absolute;\n        left: 0;\n        height: 1.1em;\n        padding: 2px 9px;\n        overflow: hidden;\n        font-size: 9px;\n        font-weight: bold;\n        color: #666;\n        _width: 700px;\n    }\n\n    .inline-group ul.tools {\n        padding: 0;\n        margin: 0;\n        list-style: none;\n    }\n\n    .inline-group ul.tools li {\n        display: inline;\n        padding: 0 5px;\n    }\n\n    .inline-group div.add-row,\n    .inline-group .tabular tr.add-row td {\n        color: #666;\n        background: #f8f8f8;\n        padding: 8px 10px;\n        border-bottom: 1px solid #eee;\n    }\n\n    .inline-group .tabular tr.add-row td {\n        padding: 8px 10px;\n        border-bottom: 1px solid #eee;\n    }\n\n    .inline-group ul.tools a.add,\n    .inline-group div.add-row a,\n    .inline-group .tabular tr.add-row td a {\n        background: url(../img/icon-addlink.svg) 0 1px no-repeat;\n        padding-left: 16px;\n        font-size: 12px;\n    }\n\n    .empty-form {\n        display: none;\n    }\n\n    /* RELATED FIELD ADD ONE / LOOKUP */\n\n    .add-another, .related-lookup {\n        margin-left: 5px;\n        display: inline-block;\n        vertical-align: middle;\n        background-repeat: no-repeat;\n        background-size: 14px;\n    }\n\n    .add-another {\n        width: 16px;\n        height: 16px;\n        background-image: url(../img/icon-addlink.svg);\n    }\n\n    .related-lookup {\n        width: 16px;\n        height: 16px;\n        background-image: url(../img/search.svg);\n    }\n\n    form .related-widget-wrapper ul {\n        display: inline-block;\n        margin-left: 6px;\n        padding-left: 3px;\n    }\n\n    .clearable-file-input input {\n        margin-top: 0;\n    }\n}"]}