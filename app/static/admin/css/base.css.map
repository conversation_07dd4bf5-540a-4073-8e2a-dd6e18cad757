{"version": 3, "sources": ["base.scss"], "names": [], "mappings": "AAIA,YACI,qBAw7BW,CAz7Bf,uDAIQ,sBAAwB,CAJhC,+BAQQ,YAAa,CARrB,yCAYQ,aAAc,CACd,oBAAqB,CAb7B,wCAiBQ,UAAW,CAjBnB,oBAqBQ,yBAA0B,CArBlC,kBAyBQ,WAAY,CAzBpB,yDA6BQ,UAAW,CACX,oBAAqB,CA9B7B,wDAkCQ,yBAA0B,CAlClC,2DAwCQ,kBAAqB,CAxC7B,cA4CQ,SAAU,CACV,gBAAiB,CA7CzB,2EAiDQ,eAAiB,CAjDzB,eAqDQ,eAAgB,CAChB,eAAgB,CAChB,cAAe,CACf,UAAW,CAxDnB,eA4DQ,cAAe,CACf,iBAAoB,CA7D5B,uBAiEQ,eAAmB,CACnB,YAAa,CAlErB,eAsEQ,cAAe,CACf,kBAAqB,CACrB,UAAW,CACX,eAAiB,CAzEzB,eA6EQ,cAAe,CACf,iBAAoB,CACpB,kBAAmB,CA/E3B,eAmFQ,cAAe,CACf,mBAAsB,CACtB,UAAW,CACX,wBAAyB,CACzB,kBAAmB,CAvF3B,kBA2FQ,sBAAuB,CACvB,aAAc,CA5FtB,kBAgGQ,eAAgB,CAhGxB,6CAoGQ,cAAe,CACf,gBAAiB,CArGzB,eAyGQ,eAAiB,CACjB,cAAe,CA1GvB,eA8GQ,aAAc,CA9GtB,iBAkHQ,QAAS,CACT,SAAU,CAnHlB,qBAuHQ,QAAS,CACT,SAAU,CAEV,WAA0B,CAA1B,yBAA0B,CA1HlC,uBA8HQ,cAAe,CACf,UAAW,CACX,eAAgB,CAChB,iBAAkB,CAClB,0BAA2B,CAlInC,iCAsIQ,yEAAkF,CAClF,UAAW,CACX,cAAe,CAxIvB,8BA4IQ,WAAY,CACZ,eAAgB,CAChB,eAAgB,CA9IxB,wBAkJQ,UAAW,CAlJnB,eAsJQ,UAAW,CACX,UAAW,CACX,qBAAsB,CACtB,UAAW,CACX,WAAY,CACZ,QAAS,CACT,SAAU,CACV,aAAc,CACd,eAAgB,CA9JxB,mBAoKQ,cAAe,CApKvB,kBAwKQ,cAAe,CAxKvB,mBA4KQ,eAAgB,CA5KxB,kBAgLQ,cAAe,CAhLvB,mBAoLQ,eAAgB,CApLxB,6DAwLQ,cAAe,CACf,UAAW,CAzLnB,0BA6LQ,WAAY,CA7LpB,iHAiMQ,qBAAsB,CAjM9B,wEAqMQ,UAAW,CACX,eAAmB,CAtM3B,yBA0MQ,WAAY,CA1MpB,wBA8MQ,UAAW,CA9MnB,mBAkNQ,UAAW,CAlNnB,wBAsNQ,eAAgB,CAtNxB,yBA0NQ,gBAAiB,CA1NzB,qBA8NQ,aAAc,CACd,gBAAiB,CACjB,kBAAmB,CAhO3B,oBAoOQ,kBAAmB,CApO3B,kBA0OQ,wBAAyB,CACzB,iBAAkB,CA3O1B,8BA+OQ,cAAe,CACf,gBAAiB,CACjB,4BAA6B,CAC7B,kBAAmB,CACnB,WAAY,CACZ,yDAAkE,CApP1E,eAwPQ,eAAgB,CAChB,eAAgB,CAzPxB,0CA8PQ,UAAW,CACX,gBAAiB,CACjB,cAAe,CACf,eAAgB,CAGhB,qBAA6B,CAA7B,iBAA6B,CAA7B,gBAA6B,CApQrC,qBAwQQ,kBAAmB,CACnB,yBAA0B,CAzQlC,8BA6QQ,UAAW,CA7QnB,mBAiRQ,kBAAmB,CAjR3B,kBAqRQ,eAAgB,CArRxB,kBAyRQ,kBAAmB,CAzR3B,qBA+RQ,gBAAiB,CACjB,kBAAmB,CACnB,wBAAyB,CACzB,kBAAmB,CAlS3B,2DAsSQ,UAAW,CAtSnB,4BA0SQ,eAAgB,CA1SxB,kCA8SQ,kBAAmB,CA9S3B,sCAkTQ,gBAAiB,CACjB,aAAc,CAnTtB,mCAuTQ,aAAc,CACd,cAAe,CACf,gBAAiB,CAzTzB,kFA6TQ,eAAgB,CA7TxB,yCAiUQ,iBAAkB,CAjU1B,qDAqUQ,kBAAmB,CArU3B,+CAyUQ,aAAc,CACd,iBAAsB,CACtB,WAAY,CACZ,gBAAiB,CA5UzB,gDAgVQ,cAAe,CACf,cAAe,CACf,iBAAkB,CAClB,kBAAmB,CACnB,eAAgB,CAChB,gBAAiB,CArVzB,iDAyVQ,iBAAkB,CAClB,UAAW,CACX,WAAY,CACZ,oBAAqB,CACrB,sDAAuD,CACvD,yBAA0B,CA9VlC,4DAkWQ,uBAAwB,CAlWhC,kEAsWQ,YAAa,CACb,iBAAkB,CAClB,QAAS,CACT,QAAS,CACT,eAAgB,CAChB,cAAe,CACf,UAAW,CA5WnB,gJAiXQ,aAAc,CAjXtB,oIAsXQ,2BAA4B,CAtXpC,2DA0XQ,2BAA4B,CA1XpC,kIA+XQ,2BAA4B,CA/XpC,4DAmYQ,OAAQ,CACR,2BAA4B,CApYpC,oIAyYQ,2BAA4B,CAzYpC,2GA+YQ,YAAa,CACb,eAAgB,CAChB,qBAAsB,CACtB,yDAAkE,CAClE,eAAmB,CACnB,cAAe,CApZvB,qBAwZQ,kBAAmB,CAxZ3B,uNA6ZQ,qBAAsB,CACtB,iBAAkB,CAClB,eAAgB,CAChB,YAAa,CAharB,uQAsaQ,iBAAkB,CAta1B,mBA0aQ,WAAY,CA1apB,6BA8aQ,gBAAiB,CA9azB,qIAobQ,kBAAmB,CACnB,iBAAkB,CAClB,WAAY,CACZ,iBAAkB,CAClB,UAAW,CACX,cAAe,CAzbvB,qBA6bQ,eAAgB,CA7bxB,+SAmcQ,kBAAmB,CAnc3B,gHAucQ,UAAY,CAvcpB,yGA2cQ,WAAY,CACZ,WAAY,CACZ,eAAgB,CAChB,kBAAmB,CA9c3B,+OAodQ,kBAAmB,CApd3B,wIA0dQ,UAAY,CA1dpB,oBAieQ,WAAY,CACZ,kBAAmB,CACnB,eAAgB,CAnexB,0IAueQ,iBAAkB,CAClB,kBAAmB,CAxe3B,+BA4eQ,gBAAiB,CA5ezB,8CAgfQ,iBAAkB,CAhf1B,uBAofQ,eAAgB,CApfxB,gFAwfQ,QAAS,CACT,WAAY,CACZ,eAAgB,CAChB,cAAe,CACf,eAAgB,CAChB,kBAAmB,CACnB,UAAW,CA9fnB,yDAmgBQ,cAAe,CACf,mBAAqB,CACrB,wBAAyB,CArgBjC,0BAygBQ,wBAAyB,CAzgBjC,2BA+gBQ,SAAU,CACV,QAAS,CAhhBjB,8BAohBQ,aAAc,CACd,eAAgB,CAChB,cAAe,CACf,2BAA4B,CAC5B,eAAkB,CAClB,4DAA6D,CAC7D,yBAA0B,CAC1B,UAAW,CA3hBnB,sCA+hBQ,8DAA+D,CAC/D,yBAA0B,CAhiBlC,oCAoiBQ,8DAA+D,CAC/D,yBAA0B,CAriBlC,uBAyiBQ,cAAe,CACf,eAAgB,CAChB,aAAc,CACd,iBAAkB,CAClB,eAAkB,CAClB,aAAc,CACd,wBAAyB,CACzB,iBAAkB,CAClB,qBAAsB,CACtB,4BAA6B,CAljBrC,yBAsjBQ,cAAe,CACf,SAAU,CACV,aAAc,CACd,eAAgB,CAzjBxB,4BA6jBQ,cAAe,CACf,aAAc,CACd,iBAAkB,CA/jB1B,wCAmkBQ,YAAa,CAnkBrB,8BAukBQ,aAAc,CACd,yBAA0B,CAxkBlC,4BA4kBQ,QAAS,CACT,SAAU,CA7kBlB,+BAilBQ,QAAS,CAjlBjB,6BAqlBQ,QAAS,CAET,WAA6B,CAA7B,4BAA6B,CAC7B,eAAgB,CAxlBxB,6CA4lBQ,cAAe,CA5lBvB,kFAgmBQ,wBAAyB,CAhmBjC,+BAomBQ,eAAgB,CAChB,WAAY,CACZ,eAAgB,CAChB,cAAe,CAvmBvB,sDA2mBQ,wBAAyB,CACzB,QAAS,CACT,UAAW,CACX,4DAA6D,CA9mBrE,yBAknBQ,cAAe,CACf,oBAAqB,CAnnB7B,4BAynBQ,cAAiB,CACjB,WAAY,CACZ,cAAe,CACf,UAAW,CACX,eAAgB,CA7nBxB,sGAqoBQ,UAAW,CAroBnB,qBA2oBQ,iBAAkB,CAClB,uDAAwD,CA5oBhE,sDAgpBQ,iBAAkB,CAClB,0DAA2D,CAjpBnE,wBAqpBQ,iBAAkB,CAClB,0DAA2D,CAtpBnE,+DA0pBQ,UAAW,CA1pBnB,8DA8pBQ,UAAW,CACX,oBAAqB,CA/pB7B,0BAqqBQ,cAAe,CACf,eAAiB,CACjB,cAAe,CACf,WAAY,CACZ,iBAAkB,CAClB,gBAAiB,CA1qBzB,oCA8qBQ,cAAe,CACf,iBAAkB,CAClB,UAAW,CACX,UAAW,CACX,kBAAmB,CAlrB3B,6BAsrBQ,aAAc,CACd,UAAW,CACX,eAAgB,CAChB,WAAY,CAzrBpB,4BA6rBQ,kBAAmB,CA7rB3B,qEAisBQ,aAAc,CACd,UAAW,CACX,gBAAiB,CACjB,eAAgB,CAChB,eAAgB,CAChB,cAAe,CACf,wBAAyB,CACzB,mBAAqB,CACrB,UAAW,CAzsBnB,oEA6sBQ,wBAAyB,CA7sBjC,kCAitBQ,oBAAqB,CAjtB7B,gHAqtBQ,2BAA4B,CAC5B,uBAA+B,CAC/B,kBAAmB,CAvtB3B,4EA2tBQ,mDAAoD,CA3tB5D,oCA+tBQ,4CAA6C,CA/tBrD,iCAquBQ,UAAW,CAruBnB,0CAyuBQ,UAAW,CAzuBnB,uBA+uBQ,iBAAkB,CAClB,UAAW,CACX,eAAgB,CAChB,SAAU,CAlvBlB,qBAsvBQ,iBAAkB,CAtvB1B,gCA0vBQ,WAAY,CA1vBpB,0BA8vBQ,UAAW,CACX,UAAW,CA/vBnB,6BAmwBQ,WAAY,CACZ,WAAY,CACZ,iBAAkB,CAClB,mBAAoB,CAtwB5B,oBA0wBQ,YAAa,CACb,UAAW,CACX,YAAa,CA5wBrB,mBAkxBQ,kBAAmB,CAlxB3B,mBAsxBQ,iBAAkB,CAtxB1B,oCA0xBQ,UAAW,CACX,cAAe,CACf,kBAAmB,CA5xB3B,iCAgyBQ,WAAY,CAhyBpB,yBAoyBQ,UAAW,CApyBnB,oBA0yBQ,UAAW,CACX,WAAY,CACZ,iBAAkB,CAClB,kBAAmB,CACnB,gBAAiB,CACjB,UAAW,CACX,eAAgB,CAhzBxB,yDAozBQ,UAAW,CApzBnB,wDAwzBQ,yBAA0B,CAxzBlC,sBA4zBQ,UAAW,CA5zBnB,yBAg0BQ,SAAU,CACV,iBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,aAAc,CAp0BtB,4FAw0BQ,aAAc,CAx0BtB,yBA40BQ,cAAe,CACf,cAAe,CACf,iBAAoB,CACpB,eAAmB,CACnB,UAAW,CAh1BnB,8BAo1BQ,oBAAqB,CAp1B7B,wBAw1BQ,WAAY,CACZ,SAAU,CACV,iBAAkB,CAClB,eAAgB,CAChB,cAAe,CACf,mBAAqB,CACrB,wBAAyB,CACzB,gBAAiB,CA/1BzB,0BAm2BQ,2CAAkD,CAn2B1D,gEAu2BQ,oBAAqB,CACrB,2BAA4B,CAC5B,aAAc,CAz2BtB,6BA+2BQ,kBAAmB,CA/2B3B,qCAm3BQ,eAAgB,CAn3BxB,gCAu3BQ,cAAe,CACf,UAAW,CACX,cAAe,CACf,eAAgB,CA13BxB,gCA83BQ,cAAe,CA93BvB,+BAk4BQ,iBAAkB,CAClB,kBAAmB,CAn4B3B,yCAu4BQ,SAAU,CACV,WAAY,CAx4BpB,4CA44BQ,eAAgB,CAChB,kBAAmB,CACnB,iBAAkB,CA94B1B,wCAk5BQ,eAAgB,CAChB,YAAa,CACb,kBAAmB,CACnB,+BAAgC,CAChC,cAAe,CACf,UAAW,CAv5BnB,yDA25BQ,kBAAmB,CACnB,iBAAkB,CAClB,iBAAkB,CAClB,UAAW,CA95BnB,8LAo6BQ,kBAAmB,CAp6B3B,mDAw6BQ,oBAAqB,CACrB,qBAAsB,CACtB,WAAY,CACZ,gBAAiB,CACjB,eAAgB,CAChB,iBAAkB,CAClB,iBAAkB,CAClB,UAAW,CACX,iBAAkB,CAh7B1B,4KAs7BQ,eAAgB,CAt7BxB,4BA27BQ,YAAa,CA37BrB,8BA+7BQ,WAAY,CA/7BpB,2BAm8BQ,iBAAkB", "file": "base.css", "sourcesContent": ["/*\n    DJANGO Admin styles\n*/\n\n.admin-area{\n    background-color: #fff;\n    \n    .CodeMirror-scroll, .CodeMirror{\n        height: 150px !important;\n    }\n\n    #changelist-filter{\n        display: none;\n    }\n    \n    a:link, a:visited {\n        color: #447e9b;\n        text-decoration: none;\n    }\n\n    a:focus, a:hover {\n        color: #036;\n    }\n\n    a:focus {\n        text-decoration: underline;\n    }\n\n    a img {\n        border: none;\n    }\n\n    a.section:link, a.section:visited {\n        color: #fff;\n        text-decoration: none;\n    }\n\n    a.section:focus, a.section:hover {\n        text-decoration: underline;\n    }\n\n    /* GLOBAL DEFAULTS */\n\n    p, ol, ul, dl {\n        margin: .2em 0 .8em 0;\n    }\n\n    p {\n        padding: 0;\n        line-height: 140%;\n    }\n\n    h1,h2,h3,h4,h5 {\n        font-weight: bold;\n    }\n\n    h1 {\n        margin: 0 0 20px;\n        font-weight: 300;\n        font-size: 20px;\n        color: #666;\n    }\n\n    h2 {\n        font-size: 16px;\n        margin: 1em 0 .5em 0;\n    }\n\n    h2.subhead {\n        font-weight: normal;\n        margin-top: 0;\n    }\n\n    h3 {\n        font-size: 14px;\n        margin: .8em 0 .3em 0;\n        color: #666;\n        font-weight: bold;\n    }\n\n    h4 {\n        font-size: 12px;\n        margin: 1em 0 .8em 0;\n        padding-bottom: 3px;\n    }\n\n    h5 {\n        font-size: 10px;\n        margin: 1.5em 0 .5em 0;\n        color: #666;\n        text-transform: uppercase;\n        letter-spacing: 1px;\n    }\n\n    ul li {\n        list-style-type: square;\n        padding: 1px 0;\n    }\n\n    li ul {\n        margin-bottom: 0;\n    }\n\n    li, dt, dd {\n        font-size: 13px;\n        line-height: 20px;\n    }\n\n    dt {\n        font-weight: bold;\n        margin-top: 4px;\n    }\n\n    dd {\n        margin-left: 0;\n    }\n\n    form {\n        margin: 0;\n        padding: 0;\n    }\n\n    fieldset {\n        margin: 0;\n        padding: 0;\n        border: none;\n        border-top: 1px solid #eee;\n    }\n\n    blockquote {\n        font-size: 11px;\n        color: #777;\n        margin-left: 2px;\n        padding-left: 10px;\n        border-left: 5px solid #ddd;\n    }\n\n    code, pre {\n        font-family: \"Bitstream Vera Sans Mono\", Monaco, \"Courier New\", Courier, monospace;\n        color: #666;\n        font-size: 12px;\n    }\n\n    pre.literal-block {\n        margin: 10px;\n        background: #eee;\n        padding: 6px 8px;\n    }\n\n    code strong {\n        color: #930;\n    }\n\n    hr {\n        clear: both;\n        color: #eee;\n        background-color: #eee;\n        height: 1px;\n        border: none;\n        margin: 0;\n        padding: 0;\n        font-size: 1px;\n        line-height: 1px;\n    }\n\n    /* TEXT STYLES & MODIFIERS */\n\n    .small {\n        font-size: 11px;\n    }\n\n    .tiny {\n        font-size: 10px;\n    }\n\n    p.tiny {\n        margin-top: -2px;\n    }\n\n    .mini {\n        font-size: 10px;\n    }\n\n    p.mini {\n        margin-top: -3px;\n    }\n\n    .help, p.help, form p.help {\n        font-size: 11px;\n        color: #999;\n    }\n\n    .help-tooltip {\n        cursor: help;\n    }\n\n    p img, h1 img, h2 img, h3 img, h4 img, td img {\n        vertical-align: middle;\n    }\n\n    .quiet, a.quiet:link, a.quiet:visited {\n        color: #999;\n        font-weight: normal;\n    }\n\n    .float-right {\n        float: right;\n    }\n\n    .float-left {\n        float: left;\n    }\n\n    .clear {\n        clear: both;\n    }\n\n    .align-left {\n        text-align: left;\n    }\n\n    .align-right {\n        text-align: right;\n    }\n\n    .example {\n        margin: 10px 0;\n        padding: 5px 10px;\n        background: #efefef;\n    }\n\n    .nowrap {\n        white-space: nowrap;\n    }\n\n    /* TABLES */\n\n    table {\n        border-collapse: collapse;\n        border-color: #ccc;\n    }\n\n    td, th {\n        font-size: 13px;\n        line-height: 16px;\n        border-bottom: 1px solid #eee;\n        vertical-align: top;\n        padding: 8px;\n        font-family: \"Roboto\", \"Lucida Grande\", Verdana, Arial, sans-serif;\n    }\n\n    th {\n        font-weight: 600;\n        text-align: left;\n    }\n\n    thead th,\n    tfoot td {\n        color: #666;\n        padding: 5px 10px;\n        font-size: 11px;\n        background: #fff;\n        border: none;\n        border-top: 1px solid #eee;\n        border-bottom: 1px solid #eee;\n    }\n\n    tfoot td {\n        border-bottom: none;\n        border-top: 1px solid #eee;\n    }\n\n    thead th.required {\n        color: #000;\n    }\n\n    tr.alt {\n        background: #f6f6f6;\n    }\n\n    .row1 {\n        background: #fff;\n    }\n\n    .row2 {\n        background: #f9f9f9;\n    }\n\n    /* SORTABLE TABLES */\n\n    thead th {\n        padding: 5px 10px;\n        line-height: normal;\n        text-transform: uppercase;\n        background: #f6f6f6;\n    }\n\n    thead th a:link, thead th a:visited {\n        color: #666;\n    }\n\n    thead th.sorted {\n        background: #eee;\n    }\n\n    thead th.sorted .text {\n        padding-right: 42px;\n    }\n\n    table thead th .text span {\n        padding: 8px 10px;\n        display: block;\n    }\n\n    table thead th .text a {\n        display: block;\n        cursor: pointer;\n        padding: 8px 10px;\n    }\n\n    table thead th .text a:focus, table thead th .text a:hover {\n        background: #eee;\n    }\n\n    thead th.sorted a.sortremove {\n        visibility: hidden;\n    }\n\n    table thead th.sorted:hover a.sortremove {\n        visibility: visible;\n    }\n\n    table thead th.sorted .sortoptions {\n        display: block;\n        padding: 9px 5px 0 5px;\n        float: right;\n        text-align: right;\n    }\n\n    table thead th.sorted .sortpriority {\n        font-size: .8em;\n        min-width: 12px;\n        text-align: center;\n        vertical-align: 3px;\n        margin-left: 2px;\n        margin-right: 2px;\n    }\n\n    table thead th.sorted .sortoptions a {\n        position: relative;\n        width: 14px;\n        height: 14px;\n        display: inline-block;\n        background: url(../img/sorting-icons.svg) 0 0 no-repeat;\n        background-size: 14px auto;\n    }\n\n    table thead th.sorted .sortoptions a.sortremove {\n        background-position: 0 0;\n    }\n\n    table thead th.sorted .sortoptions a.sortremove:after {\n        content: '\\\\';\n        position: absolute;\n        top: -6px;\n        left: 3px;\n        font-weight: 200;\n        font-size: 18px;\n        color: #999;\n    }\n\n    table thead th.sorted .sortoptions a.sortremove:focus:after,\n    table thead th.sorted .sortoptions a.sortremove:hover:after {\n        color: #447e9b;\n    }\n\n    table thead th.sorted .sortoptions a.sortremove:focus,\n    table thead th.sorted .sortoptions a.sortremove:hover {\n        background-position: 0 -14px;\n    }\n\n    table thead th.sorted .sortoptions a.ascending {\n        background-position: 0 -28px;\n    }\n\n    table thead th.sorted .sortoptions a.ascending:focus,\n    table thead th.sorted .sortoptions a.ascending:hover {\n        background-position: 0 -42px;\n    }\n\n    table thead th.sorted .sortoptions a.descending {\n        top: 1px;\n        background-position: 0 -56px;\n    }\n\n    table thead th.sorted .sortoptions a.descending:focus,\n    table thead th.sorted .sortoptions a.descending:hover {\n        background-position: 0 -70px;\n    }\n\n    /* FORM DEFAULTS */\n\n    input, textarea, select, .form-row p, form .button {\n        margin: 2px 0;\n        padding: 2px 3px;\n        vertical-align: middle;\n        font-family: \"Roboto\", \"Lucida Grande\", Verdana, Arial, sans-serif;\n        font-weight: normal;\n        font-size: 13px;\n    }\n\n    textarea {\n        vertical-align: top;\n    }\n\n    input[type=text], input[type=password], input[type=email], input[type=url],\n    input[type=number], textarea, select, .vTextField {\n        border: 1px solid #ccc;\n        border-radius: 4px;\n        padding: 5px 6px;\n        margin-top: 0;\n    }\n\n    input[type=text]:focus, input[type=password]:focus, input[type=email]:focus,\n    input[type=url]:focus, input[type=number]:focus, textarea:focus, select:focus,\n    .vTextField:focus {\n        border-color: #999;\n    }\n\n    select {\n        height: 30px;\n    }\n\n    select[multiple] {\n        min-height: 150px;\n    }\n\n    /* FORM BUTTONS */\n\n    .button, input[type=submit], input[type=button], .submit-row input, a.button {\n        background: #18bc9c;\n        padding: 10px 15px;\n        border: none;\n        border-radius: 4px;\n        color: #fff;\n        cursor: pointer;\n    }\n\n    a.button {\n        padding: 4px 5px;\n    }\n\n    .button:active, input[type=submit]:active, input[type=button]:active,\n    .button:focus, input[type=submit]:focus, input[type=button]:focus,\n    .button:hover, input[type=submit]:hover, input[type=button]:hover {\n        background: #609ab6;\n    }\n\n    .button[disabled], input[type=submit][disabled], input[type=button][disabled] {\n        opacity: 0.4;\n    }\n\n    .button.default, input[type=submit].default, .submit-row input.default {\n        float: right;\n        border: none;\n        font-weight: 400;\n        background: #417690;\n    }\n\n    .button.default:active, input[type=submit].default:active,\n    .button.default:focus, input[type=submit].default:focus,\n    .button.default:hover, input[type=submit].default:hover {\n        background: #205067;\n    }\n\n    .button[disabled].default,\n    input[type=submit][disabled].default,\n    input[type=button][disabled].default {\n        opacity: 0.4;\n    }\n\n\n    /* MODULES */\n\n    .module {\n        border: none;\n        margin-bottom: 30px;\n        background: #fff;\n    }\n\n    .module p, .module ul, .module h3, .module h4, .module dl, .module pre {\n        padding-left: 10px;\n        padding-right: 10px;\n    }\n\n    .module blockquote {\n        margin-left: 12px;\n    }\n\n    .module ul, .module ol {\n        margin-left: 1.5em;\n    }\n\n    .module h3 {\n        margin-top: .6em;\n    }\n\n    .module h2, .module caption, .inline-group h2 {\n        margin: 0;\n        padding: 8px;\n        font-weight: 400;\n        font-size: 13px;\n        text-align: left;\n        background: #18bc9c;\n        color: #fff;\n    }\n\n    .module caption,\n    .inline-group h2 {\n        font-size: 12px;\n        letter-spacing: 0.5px;\n        text-transform: uppercase;\n    }\n\n    .module table {\n        border-collapse: collapse;\n    }\n\n    /* MESSAGES & ERRORS */\n\n    ul.messagelist {\n        padding: 0;\n        margin: 0;\n    }\n\n    ul.messagelist li {\n        display: block;\n        font-weight: 400;\n        font-size: 13px;\n        padding: 10px 10px 10px 65px;\n        margin: 0 0 10px 0;\n        background: #dfd url(../img/icon-yes.svg) 40px 12px no-repeat;\n        background-size: 16px auto;\n        color: #333;\n    }\n\n    ul.messagelist li.warning {\n        background: #ffc url(../img/icon-alert.svg) 40px 14px no-repeat;\n        background-size: 14px auto;\n    }\n\n    ul.messagelist li.error {\n        background: #ffefef url(../img/icon-no.svg) 40px 12px no-repeat;\n        background-size: 16px auto;\n    }\n\n    .errornote {\n        font-size: 14px;\n        font-weight: 700;\n        display: block;\n        padding: 10px 12px;\n        margin: 0 0 10px 0;\n        color: #ba2121;\n        border: 1px solid #ba2121;\n        border-radius: 4px;\n        background-color: #fff;\n        background-position: 5px 12px;\n    }\n\n    ul.errorlist {\n        margin: 0 0 4px;\n        padding: 0;\n        color: #ba2121;\n        background: #fff;\n    }\n\n    ul.errorlist li {\n        font-size: 13px;\n        display: block;\n        margin-bottom: 4px;\n    }\n\n    ul.errorlist li:first-child {\n        margin-top: 0;\n    }\n\n    ul.errorlist li a {\n        color: inherit;\n        text-decoration: underline;\n    }\n\n    td ul.errorlist {\n        margin: 0;\n        padding: 0;\n    }\n\n    td ul.errorlist li {\n        margin: 0;\n    }\n\n    .form-row.errors {\n        margin: 0;\n        border: none;\n        border-bottom: 1px solid #eee;\n        background: none;\n    }\n\n    .form-row.errors ul.errorlist li {\n        padding-left: 0;\n    }\n\n    .errors input, .errors select, .errors textarea {\n        border: 1px solid #ba2121;\n    }\n\n    div.system-message {\n        background: #ffc;\n        margin: 10px;\n        padding: 6px 8px;\n        font-size: .8em;\n    }\n\n    div.system-message p.system-message-title {\n        padding: 4px 5px 4px 25px;\n        margin: 0;\n        color: #c11;\n        background: #ffefef url(../img/icon-no.svg) 5px 5px no-repeat;\n    }\n\n    .description {\n        font-size: 12px;\n        padding: 5px 0 0 12px;\n    }\n\n    /* BREADCRUMBS */\n\n    div.breadcrumbs {\n        padding: 10px 0px;\n        border: none;\n        font-size: 14px;\n        color: #000;\n        text-align: left;\n    }\n\n    div.breadcrumbs a {\n        color: #000;\n    }\n\n    div.breadcrumbs a:focus, div.breadcrumbs a:hover {\n        color: #000;\n    }\n\n    /* ACTION ICONS */\n\n    .addlink {\n        padding-left: 16px;\n        background: url(../img/icon-addlink.svg) 0 1px no-repeat;\n    }\n\n    .changelink, .inlinechangelink {\n        padding-left: 16px;\n        background: url(../img/icon-changelink.svg) 0 1px no-repeat;\n    }\n\n    .deletelink {\n        padding-left: 16px;\n        background: url(../img/icon-deletelink.svg) 0 1px no-repeat;\n    }\n\n    a.deletelink:link, a.deletelink:visited {\n        color: #fff;\n    }\n\n    a.deletelink:focus, a.deletelink:hover {\n        color: #fff;\n        text-decoration: none;\n    }\n\n    /* OBJECT TOOLS */\n\n    .object-tools {\n        font-size: 10px;\n        font-weight: bold;\n        padding-left: 0;\n        float: right;\n        position: relative;\n        margin-top: -48px;\n    }\n\n    .form-row .object-tools {\n        margin-top: 5px;\n        margin-bottom: 5px;\n        float: none;\n        height: 2em;\n        padding-left: 3.5em;\n    }\n\n    .object-tools li {\n        display: block;\n        float: left;\n        margin-left: 5px;\n        height: 16px;\n    }\n\n    .object-tools a {\n        border-radius: 15px;\n    }\n\n    .object-tools a:link, .object-tools a:visited {\n        display: block;\n        float: left;\n        padding: 3px 12px;\n        background: #999;\n        font-weight: 400;\n        font-size: 11px;\n        text-transform: uppercase;\n        letter-spacing: 0.5px;\n        color: #fff;\n    }\n\n    .object-tools a:focus, .object-tools a:hover {\n        background-color: #417690;\n    }\n\n    .object-tools a:focus{\n        text-decoration: none;\n    }\n\n    .object-tools a.viewsitelink, .object-tools a.golink,.object-tools a.addlink {\n        background-repeat: no-repeat;\n        background-position: 93% center;\n        padding-right: 26px;\n    }\n\n    .object-tools a.viewsitelink, .object-tools a.golink {\n        background-image: url(../img/tooltag-arrowright.svg);\n    }\n\n    .object-tools a.addlink {\n        background-image: url(../img/tooltag-add.svg);\n    }\n\n    /* OBJECT HISTORY */\n\n    table#change-history {\n        width: 100%;\n    }\n\n    table#change-history tbody th {\n        width: 16em;\n    }\n\n    /* PAGE STRUCTURE */\n\n    #container {\n        position: relative;\n        width: 100%;\n        min-width: 980px;\n        padding: 0;\n    }\n\n    #content {\n        padding: 20px 40px;\n    }\n\n    .dashboard #content {\n        width: 600px;\n    }\n\n    #content-main {\n        float: left;\n        width: 100%;\n    }\n\n    #content-related {\n        float: right;\n        width: 260px;\n        position: relative;\n        margin-right: -300px;\n    }\n\n    #footer {\n        display: none;\n        clear: both;\n        padding: 10px;\n    }\n\n    /* COLUMN TYPES */\n\n    .colMS {\n        margin-right: 300px;\n    }\n\n    .colSM {\n        margin-left: 300px;\n    }\n\n    .colSM #content-related {\n        float: left;\n        margin-right: 0;\n        margin-left: -300px;\n    }\n\n    .colSM #content-main {\n        float: right;\n    }\n\n    .popup .colM {\n        width: auto;\n    }\n\n    /* HEADER */\n\n    #header {\n        width: auto;\n        height: 40px;\n        padding: 10px 40px;\n        background: #417690;\n        line-height: 40px;\n        color: #ffc;\n        overflow: hidden;\n    }\n\n    #header a:link, #header a:visited {\n        color: #fff;\n    }\n\n    #header a:focus , #header a:hover {\n        text-decoration: underline;\n    }\n\n    #branding {\n        float: left;\n    }\n\n    #branding h1 {\n        padding: 0;\n        margin: 0 20px 0 0;\n        font-weight: 300;\n        font-size: 24px;\n        color: #f5dd5d;\n    }\n\n    #branding h1, #branding h1 a:link, #branding h1 a:visited {\n        color: #f5dd5d;\n    }\n\n    #branding h2 {\n        padding: 0 10px;\n        font-size: 14px;\n        margin: -8px 0 8px 0;\n        font-weight: normal;\n        color: #ffc;\n    }\n\n    #branding a:hover {\n        text-decoration: none;\n    }\n\n    #user-tools {\n        float: right;\n        padding: 0;\n        margin: 0 0 0 20px;\n        font-weight: 300;\n        font-size: 11px;\n        letter-spacing: 0.5px;\n        text-transform: uppercase;\n        text-align: right;\n    }\n\n    #user-tools a {\n        border-bottom: 1px solid rgba(255, 255, 255, 0.25);\n    }\n\n    #user-tools a:focus, #user-tools a:hover {\n        text-decoration: none;\n        border-bottom-color: #18bc9c;\n        color: #18bc9c;\n    }\n\n    /* SIDEBAR */\n\n    #content-related {\n        background: #f8f8f8;\n    }\n\n    #content-related .module {\n        background: none;\n    }\n\n    #content-related h3 {\n        font-size: 14px;\n        color: #666;\n        padding: 0 16px;\n        margin: 0 0 16px;\n    }\n\n    #content-related h4 {\n        font-size: 13px;\n    }\n\n    #content-related p {\n        padding-left: 16px;\n        padding-right: 16px;\n    }\n\n    #content-related .actionlist {\n        padding: 0;\n        margin: 16px;\n    }\n\n    #content-related .actionlist li {\n        line-height: 1.2;\n        margin-bottom: 10px;\n        padding-left: 18px;\n    }\n\n    #content-related .module h2 {\n        background: none;\n        padding: 16px;\n        margin-bottom: 16px;\n        border-bottom: 1px solid #eaeaea;\n        font-size: 18px;\n        color: #333;\n    }\n\n    .delete-confirmation form input[type=\"submit\"] {\n        background: #ba2121;\n        border-radius: 4px;\n        padding: 10px 15px;\n        color: #fff;\n    }\n\n    .delete-confirmation form input[type=\"submit\"]:active,\n    .delete-confirmation form input[type=\"submit\"]:focus,\n    .delete-confirmation form input[type=\"submit\"]:hover {\n        background: #a41515;\n    }\n\n    .delete-confirmation form .cancel-link {\n        display: inline-block;\n        vertical-align: middle;\n        height: 15px;\n        line-height: 15px;\n        background: #ddd;\n        border-radius: 4px;\n        padding: 10px 15px;\n        color: #333;\n        margin: 0 0 0 10px;\n    }\n\n    .delete-confirmation form .cancel-link:active,\n    .delete-confirmation form .cancel-link:focus,\n    .delete-confirmation form .cancel-link:hover {\n        background: #ccc;\n    }\n\n    /* POPUP */\n    .popup #content {\n        padding: 20px;\n    }\n\n    .popup #container {\n        min-width: 0;\n    }\n\n    .popup #header {\n        padding: 10px 20px;\n    }\n}"]}