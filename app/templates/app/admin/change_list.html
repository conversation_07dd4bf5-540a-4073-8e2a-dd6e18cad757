{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" type="text/css" href="{% static "admin/css/changelists.css" %}" />
  {% if cl.formset %}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}" />
  {% endif %}
  {% if cl.formset or action_form %}
    <script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
  {% endif %}
  {{ media.css }}
  {% if not actions_on_top and not actions_on_bottom %}
    <style>
      #changelist table thead th:first-child {width: inherit}
    </style>
  {% endif %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
{{ media.js }}
{% endblock %}

{% block bodyclass %}{{ block.super }} app-{{ opts.app_label }} model-{{ opts.model_name }} change-list{% endblock %}

{% if not is_popup %}
{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=cl.opts.app_label %}">{{ cl.opts.app_config.verbose_name }}</a>
&rsaquo; {{ cl.opts.verbose_name_plural|capfirst }}
</div>
{% endblock %}
{% endif %}

{% block coltype %}flex{% endblock %}

{% block content %}
  <div id="content-main">
    {% block object-tools %}
        <ul class="object-tools">
          {% block object-tools-items %}
            {% if has_add_permission %}
            <li>
              {% url cl.opts|admin_urlname:'add' as add_url %}
              <a href="{% add_preserved_filters add_url is_popup to_field %}" class="addlink">
                {% blocktrans with cl.opts.verbose_name as name %}Add {{ name }}{% endblocktrans %}
              </a>
            </li>
            {% endif %}
          {% endblock %}
        </ul>
    {% endblock %}
    {% if cl.formset.errors %}
        <p class="errornote">
        {% if cl.formset.total_error_count == 1 %}{% trans "Please correct the error below." %}{% else %}{% trans "Please correct the errors below." %}{% endif %}
        </p>
        {{ cl.formset.non_form_errors }}
    {% endif %}
    <div class="module{% if cl.has_filters %} filtered{% endif %}" id="changelist">
      {% block search %}{% search_form cl %}{% endblock %}
      {% block date_hierarchy %}{% date_hierarchy cl %}{% endblock %}

      {% block filters %}
        {% if cl.has_filters %}
          <div id="changelist-filter">
            <h2>{% trans 'Filter' %}</h2>
            {% for spec in cl.filter_specs %}{% admin_list_filter cl spec %}{% endfor %}
          </div>
        {% endif %}
      {% endblock %}

      <form id="changelist-form" method="post"{% if cl.formset.is_multipart %} enctype="multipart/form-data"{% endif %} novalidate>{% csrf_token %}
      {% if cl.formset %}
        <div>{{ cl.formset.management_form }}</div>
      {% endif %}

      {% block result_list %}
          {% if action_form and actions_on_top and cl.show_admin_actions %}{% admin_actions %}{% endif %}
          {% result_list cl %}
          {% if action_form and actions_on_bottom and cl.show_admin_actions %}{% admin_actions %}{% endif %}
      {% endblock %}
      {% block pagination %}{% pagination cl %}{% endblock %}
      </form>
    </div>
  </div>
{% endblock %}
