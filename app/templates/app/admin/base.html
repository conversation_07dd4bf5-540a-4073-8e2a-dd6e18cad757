{% extends "app/logged_in_base.html" %}
{% load i18n static %}

{% block extra-headers %}
    <link rel="stylesheet" type="text/css" href="{% block stylesheet %}{% static "admin/css/base.css" %}{% endblock %}" />
    {% block extrastyle %}{% endblock %}
    {% block extrahead %}{% endblock %}
    {% block blockbots %}<meta name="robots" content="NONE,NOARCHIVE" />{% endblock %}
{% endblock %}

{% block page-wrapper %}
<div id="page-wrapper" class="admin-area">
    <!-- Content -->
    <section class="main">
        <div class="content {% block coltype %}colM{% endblock %}">
            {{ SETTINGS.theme.html_after_header|safe }}
            {% block breadcrumbs %}
            <div class="breadcrumbs">
            <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
            {% if title %} &rsaquo; {{ title }}{% endif %}
            </div>
            {% endblock %}

            {% block messages %}
                {% if messages %}
                <ul class="messagelist">{% for message in messages %}
                  <li{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message|capfirst }}</li>
                {% endfor %}</ul>
                {% endif %}
            {% endblock messages %}


            {% block pretitle %}{% endblock %}
            {% block content_title %}{% if title %}<h1>{{ title }}</h1>{% endif %}{% endblock %}
            {% block content %}
            {% block object-tools %}{% endblock %}
            {{ content }}
            {% endblock %}
            {% block sidebar %}{% endblock %}
            <br class="clear" />
        </div>
    </section>
    <!-- END Content -->

    {% block footer %}<div id="footer"></div>{% endblock %}
</div>
{% endblock %}