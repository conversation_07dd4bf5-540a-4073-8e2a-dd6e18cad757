{% load i18n static %}
<div class="related-widget-wrapper">
    {{ widget }}
    {% block links %}
        {% if can_change_related %}
        <a class="related-widget-wrapper-link change-related" id="change_id_{{ name }}"
            data-href-template="{{ change_related_template_url }}?{{ url_params }}"
            title="{% blocktrans %}Change selected {{ model }}{% endblocktrans %}">
            <img src="{% static 'admin/img/icon-changelink.svg' %}" alt="{% trans 'Change' %}"/>
        </a>
        {% endif %}
        {% if can_add_related %}
        <a class="related-widget-wrapper-link add-related" id="add_id_{{ name }}"
            href="{{ add_related_url }}?{{ url_params }}"
            title="{% blocktrans %}Add another {{ model }}{% endblocktrans %}">
            <img src="{% static 'admin/img/icon-addlink.svg' %}" alt="{% trans 'Add' %}"/>
        </a>
        {% endif %}
        {% if can_delete_related %}
        <a class="related-widget-wrapper-link delete-related" id="delete_id_{{ name }}"
            data-href-template="{{ delete_related_template_url }}?{{ url_params }}"
            title="{% blocktrans %}Delete selected {{ model }}{% endblocktrans %}">
            <img src="{% static 'admin/img/icon-deletelink.svg' %}" alt="{% trans 'Delete' %}"/>
        </a>
        {% endif %}
    {% endblock %}
</div>
