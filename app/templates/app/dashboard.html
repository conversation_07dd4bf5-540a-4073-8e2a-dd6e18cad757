{% extends "app/logged_in_base.html" %}
{% load i18n %}
{% load settings %}

{% block content %}
	<!--[if lte IE 8]>
		<div class="alert alert-warning alert-dismissible">
            WebODM cannot be used with this version of Internet Explorer. Could you please upgrade <a href="https://www.microsoft.com/en-us/download/internet-explorer.aspx">Internet Explorer</a> to the latest version or use <a href="https://www.mozilla.org/en-US/firefox/new/">Firefox</a>?
        </div>
	<![endif]-->

	{% if no_processingnodes %}
		{% include "quota.html" %}

		<h3>{% trans 'Welcome!' %} ☺</h3>
		{% trans 'Add a Processing Node' as add_processing_node %}
		{% with nodeodm_link='<a href="https://github.com/OpenDroneMap/NodeODM" target="_blank">NodeODM</a>' api_link='<a href="https://github.com/OpenDroneMap/NodeODM/blob/master/docs/index.adoc" target="_blank">API</a>' %}
			<p>
			{% blocktrans %}To get started, "{{ add_processing_node }}". A processing node is a computer running an instance of {{ nodeodm_link }} or some other software implementing this {{ api_link }}.{% endblocktrans %}
			</p>
		{% endwith %}
		<button class="btn btn-primary" onclick="location.href='{% url "admin:nodeodm_processingnode_add" %}';"><i class="fa fa-plus-circle"></i> {{ add_processing_node }}</button>
		
	{% else %}
		{% include "quota.html" %}

		{% if no_tasks and onboarding %}
			<h3>{% trans 'Welcome!' %} ☺</h3>
			{% trans 'Select Images and GCP' as upload_images %}
			<p>
				{% blocktrans %}To create a map, press the "{{ upload_images }}" button, or drag and drop some images into a project.{% endblocktrans %}
			</p>
			<p>
				<ul>
					<li>{% trans 'You need at least 5 images, but 16-32 is typically the minimum.' %}</li>
					<li>{% trans 'Images must overlap by 65% or more. Aim for 70-72%' %}</li>
					<li>{% trans 'For great 3D, images must overlap by 83%' %}</li>
					<li>{% gcp_docs_link as gcp_link %}{% blocktrans with link_start=gcp_link|safe link_end='</a>' %}A {{link_start}}GCP File{{link_end}} is optional, but can increase georeferencing accuracy{% endblocktrans %}</li>
				</ul>
			</p>
		{% endif %}
		
		<div id="dashboard-app" data-dashboard
			{% for key, value in params %}
				data-{{key}}="{{value}}"
			{% endfor %}
		>
			<div id="dashboard-placeholder" style="height: 100%">
				<div style="display: flex; margin-top: 4px">
					<div style="width: 90%"></div>
					<div class="placeholder-item border shimmer" style="width: 10%; height: 34px; margin-right: 4px;"></div>
				</div>
				<div style="display: flex; margin-top: 4px">
					<div style="width: 95%"></div>
					<div class="placeholder-item border shimmer" style="width: 5%; height: 28px; margin-right: 4px;"></div>
				</div>
			
				<div class="placeholder-item pulse border shimmer" style="height: 100px; margin-top: 16px; width: 100%;"></div>
				<div class="placeholder-item pulse border shimmer" style="height: 100px; margin-top: 16px; width: 100%;"></div>
				<div class="placeholder-item pulse border shimmer" style="height: 100px; margin-top: 16px; width: 100%;"></div>
			</div>
		</div>
		
	{% endif %}

	{% load render_bundle from webpack_loader %}
	{% render_bundle 'Dashboard' attrs='async' %}
{% endblock %}
