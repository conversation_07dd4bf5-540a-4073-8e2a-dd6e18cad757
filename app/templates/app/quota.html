{% load i18n %}
{% load settings %}

{% quota_exceeded_grace_period as when %}

{% if user.profile.has_exceeded_quota_cached %}
    {% with total=user.profile.quota|disk_size used=user.profile.used_quota_cached|disk_size %}
    <div class="alert alert-warning alert-dismissible">
        <i class="fas fa-info-circle"></i> {% blocktrans %}The disk quota is being exceeded ({{ used }} of {{ total }} used). The most recent tasks will be automatically deleted {{ when }}, until usage falls below {{ total }}.{% endblocktrans %}
    </div>
    {% endwith %}
{% elif user.profile.quota == 0 %}
    <div class="alert alert-warning alert-dismissible">
        <i class="fas fa-info-circle"></i> {% blocktrans %}Your account does not have a storage quota. Any new task will be automatically deleted {{ when }}{% endblocktrans %}
    </div>
{% endif %}