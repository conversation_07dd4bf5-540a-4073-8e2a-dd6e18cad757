{% extends 'registration/registration_base.html' %}
{% load i18n %}

{% block registration_content %}
    {% if validlink %}
        {% if form.errors %}
            <div class="alert alert-warning">
                <a class="close" data-dismiss="alert" href="#">×</a>
                <p>{% blocktrans count form.errors.items|length as counter %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktrans %}</p>
            </div>
        {% endif %}
        <form action="" method="post">{% csrf_token %}
            <div class="clearfix">
                <div class="input">
                    {% trans "Please enter your new password twice so we can verify you typed it in correctly." %}
                </div>
            </div>
            {% for field in form %}
                {% include 'registration/form_field.html' %}
            {% endfor %}
            <div class="actions">
                <input type="submit" class="btn btn-primary" value="{% trans 'Change My Password' %}">
            </div>
        </form>
    {% else %}
        <h3>{% trans 'Password Reset Failed' %}</h3>
        <p>
            {% trans "The password reset link was invalid, possibly because it has already been used.  Please request a new password reset." %}
        </p>
    {% endif %}
{% endblock %}