{% extends 'registration/registration_base.html' %}
{% load i18n %}

{% block registration_content %}
    {% if form.errors %}
            <div class="alert alert-warning">
            <p>{% blocktrans count form.errors.items|length as counter %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktrans %}</p>
        </div>
    {% endif %}
    <div>
    <form action="" method="post" class="form-horizontal">{% csrf_token %}
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10 text-info">
                {% trans "Please enter your old password, for security's sake, and then enter your new password twice so we can verify you typed it in correctly." %}
            </div>
        </div>
        {% for field in form %}
            {% include 'registration/form_field.html' %}
        {% endfor %}
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
              <button type="submit" class="btn btn-default">{% trans 'Change my password' %}</button>
              &nbsp;<button type="reset" class="btn">{% trans 'Cancel' %}</button>
            </div>
        </div>
    </form>
    </div>

{% endblock %}