{% extends 'registration/registration_base.html' %}
{% load i18n %}

{% block registration_content %}
    {% if form.errors %}
        <div class="alert alert-warning">
            <p>{% blocktrans count form.errors.items|length as counter %}Please correct the error below.{% plural %}Please correct the errors below.{% endblocktrans %}</p>
        </div>
    {% endif %}
    <form action="" method="post" class="form-horizontal">{% csrf_token %}
        {% for field in form %}
            {% include 'registration/form_field.html' %}
        {% endfor %}
        <div class="form-group">
            <div class="col-sm-offset-2 col-sm-10">
                <button id='submit' class="btn btn-primary" type="submit" data-loading-text="{% trans 'loading' %}..." accesskey="Enter">{% trans 'Reset My Password' %}</button>
            </div>
        </div>
    </form>
{% endblock %}
