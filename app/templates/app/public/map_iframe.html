{% extends "app/public/iframe_base.html" %}

{% block content %}
    <style type="text/css">
    #map-placeholder-header{
        display: none !important;
    }
    #map-placeholder-map{
        position: absolute; top: 0; bottom: 0; left: 0; right:0;
        margin-top: 0 !important;
        height: 100% !important;
    }
    </style>

    <div data-mapview
        {% for key, value in params %}
            data-{{key}}="{{value}}"
        {% endfor %}
    >
        {% include "map_placeholder.html" %}
    </div>
    {% load render_bundle from webpack_loader %}
    {% render_bundle 'MapView' attrs='async' %}
{% endblock %}