{% extends "app/logged_in_base.html" %}
{% load i18n tz %}

{% block content %}

{% load render_bundle from webpack_loader %}
{% render_bundle 'Console' attrs='async' %}

<table class="table table-bordered table-striped table-first-col-bold processing-node-info">
	<tr>
		<td>{% trans "Hostname" %}</td>
		<td>{{ processing_node.hostname }}</td>
	</tr>
	<tr>
		<td>{% trans "Port" %}</td>
		<td>{{ processing_node.port }}</td>
	</tr>
	<tr>
		<td>{% trans "API Version" %}</td>
		<td>{{ processing_node.api_version }}</td>
	</tr>
	<tr>
		<td>{% trans "Engine" %}</td>
		<td>{{ processing_node.engine }}</td>
	</tr>
	<tr>
		<td>{% trans "Engine Version" %}</td>
		<td>{{ processing_node.engine_version }}</td>
	</tr>
	<tr>
		<td>{% trans "Queue Count" %}</td>
		<td>{{ processing_node.queue_count }}</td>
	</tr>
	<tr>
		<td>{% trans "Max Images Limit" %}</td>
		<td>{{ processing_node.max_images }}</td>
	</tr>
	<tr>
		<td>{% trans "Label" %}</td>
		<td>{{ processing_node.label|default:"None" }}</td>
	</tr>
	<tr>
		<td>{% trans "Last Refreshed" %}</td>
		<td>{{ processing_node.last_refreshed|timesince }} {% trans 'ago' context '5 minutes ago' %} ({{ processing_node.last_refreshed|localtime }})</td> <!-- TODO: timezone? -->
	</tr>
	<tr>
		<td>{% trans "Options (JSON)" %}</td>
		<td><div data-console data-console-lang="js" data-console-height="300">{{ available_options_json}}</div></td>
	</tr>
</table>
{% if user.is_superuser %} 
<div class="text-center">
	<button class="btn btn-default" onclick="location.href='{% url "admin:nodeodm_processingnode_change" processing_node.id %}';"><i class="glyphicon glyphicon-pencil"></i> {% trans "Edit" %}</button>
	<button class="btn btn-danger" onclick="location.href='{% url "admin:nodeodm_processingnode_delete" processing_node.id  %}';"><i class="glyphicon glyphicon-trash"></i> {% trans "Delete" %}</button>
</div>
{% endif %}

{% endblock %}
